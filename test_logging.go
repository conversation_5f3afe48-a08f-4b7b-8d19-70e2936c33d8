package main

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"time"

	"github.com/go-chi/chi/v5/middleware"
	"github.com/information-sharing-networks/signalsd/app/internal/logger"
)

func main() {
	// Initialize logger
	log := logger.InitLogger(slog.LevelDebug, "dev")
	slog.SetDefault(log)

	// Create a simple HTTP handler that adds attributes to context
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Add some attributes to the context - these should appear in the middleware log
		logger.ContextWithLogAttrs(r.Context(),
			slog.String("user_id", "12345"),
			slog.String("operation", "test_operation"),
		)

		// Check if attributes are in context
		attrs := logger.ContextLogAttrs(r.Context())
		fmt.Printf("Hand<PERSON> sees attributes: %v\n", attrs)

		// Log something from the handler
		reqLogger := logger.ContextLogger(r.Context())
		reqLogger.Info("Handler executed successfully")

		w.<PERSON><PERSON><PERSON>ead<PERSON>(http.StatusOK)
		w.Write([]byte("OK"))
	})

	// Wrap with request ID middleware and our logging middleware
	wrappedHandler := middleware.RequestID(
		logger.RequestLogging(log)(handler),
	)

	// Create a test request
	req, _ := http.NewRequest("GET", "/test", nil)
	req = req.WithContext(context.Background())

	// Create a response recorder
	w := &testResponseWriter{header: make(http.Header)}

	// Execute the request
	fmt.Println("=== Testing Context Attribute Flow ===")
	wrappedHandler.ServeHTTP(w, req)
	fmt.Printf("Response status: %d\n", w.statusCode)
}

// Simple response writer for testing
type testResponseWriter struct {
	header     http.Header
	body       []byte
	statusCode int
}

func (w *testResponseWriter) Header() http.Header {
	return w.header
}

func (w *testResponseWriter) Write(data []byte) (int, error) {
	if w.statusCode == 0 {
		w.statusCode = 200
	}
	w.body = append(w.body, data...)
	return len(data), nil
}

func (w *testResponseWriter) WriteHeader(statusCode int) {
	w.statusCode = statusCode
}
